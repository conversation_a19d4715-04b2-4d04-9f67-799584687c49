import pandas as pd
import numpy as np
import joblib
from datetime import datetime, timedelta
import yfinance as yf
import matplotlib.pyplot as plt
from tensorflow.keras.models import load_model
import pandas_ta as ta

def predict_tomorrow_bitcoin_price(model_name="LSTM"):
    """
    Belirtilen modeli kullanarak yarınki Bitcoin fiyatını tahmin eder
    
    Args:
        model_name (str): <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> model ("LSTM", "LinearRegression", veya "RandomForest")
        
    Returns:
        float: Yarınki Bitcoin fiyat tahmini
    """
    print(f"{model_name} modeli kullanılarak yarınki Bitcoin fiyatı tahmin ediliyor...")
    
    # Bugünün tarihi
    today = datetime.now().strftime("%Y-%m-%d")
    tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    
    # En son Bitcoin verilerini indir (son 60 gün)
    start_date = (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d")
    btc_data = yf.download("BTC-USD", start=start_date, end=today)
    
    # En son Altın verilerini indir
    gold_data = yf.download("GC=F", start=start_date, end=today)
    
    # En son Petrol verilerini indir
    oil_data = yf.download("CL=F", start=start_date, end=today)
    
    # Ortak tarihleri bul
    btc_dates = set(btc_data.index)
    gold_dates = set(gold_data.index)
    oil_dates = set(oil_data.index)
    common_dates = list(btc_dates.intersection(gold_dates).intersection(oil_dates))
    
    # Ortak tarihlere göre filtrele
    btc_data = btc_data.loc[common_dates]
    gold_data = gold_data.loc[common_dates]
    oil_data = oil_data.loc[common_dates]
    
    # Sütun isimlerini yeniden adlandır
    gold_data = gold_data.add_prefix('Gold_')
    oil_data = oil_data.add_prefix('Oil_')
    
    # Veri setlerini birleştir
    merged_data = pd.concat([btc_data, gold_data, oil_data], axis=1)
    
    # Teknik göstergeleri hesapla
    merged_data["RSI"] = ta.rsi(merged_data.Close, length=15)
    merged_data["EMAF"] = ta.ema(merged_data.Close, length=20)
    merged_data["EMAM"] = ta.ema(merged_data.Close, length=100)
    merged_data["EMAS"] = ta.ema(merged_data.Close, length=150)
    
    # NaN değerleri kaldır
    merged_data.dropna(inplace=True)
    
    # Model ve ölçekleyicileri yükle
    model_dir = f"models/{model_name}"
    model_path = f"{model_dir}/{model_name}_model.{'h5' if model_name == 'LSTM' else 'pkl'}"
    scaler_x_path = f"{model_dir}/{model_name}_scaler_X.pkl"
    scaler_y_path = f"{model_dir}/{model_name}_scaler_y.pkl"
    
    # Ölçekleyicileri yükle
    sc_X = joblib.load(scaler_x_path)
    sc_y = joblib.load(scaler_y_path)
    
    # Özellikleri hazırla
    cols_to_drop = ['Close', 'Volume'] if 'Volume' in merged_data.columns else ['Close']
    features = merged_data.drop(columns=cols_to_drop)
    
    # Özellikleri ölçeklendir
    features_scaled = sc_X.transform(features)
    
    # Model tipine göre tahmin yap
    backcandles = 30
    if model_name == "LSTM":
        # LSTM için, sıralı veri gerekiyor (son 30 gün)
        X_seq = np.array([features_scaled[-backcandles:]])
        model = load_model(model_path)
        prediction_scaled = model.predict(X_seq, verbose=0)
    else:
        # Diğer modeller için sadece en son veri noktası gerekiyor
        X = features_scaled[-1:] 
        model = joblib.load(model_path)
        prediction_scaled = model.predict(X)
    
    # Gerçek fiyatı elde etmek için ters dönüşüm
    prediction = sc_y.inverse_transform(prediction_scaled.reshape(-1, 1))
    
    print(f"{tomorrow} için tahmin edilen Bitcoin fiyatı: ${prediction[0][0]:.2f}")
    return prediction[0][0]

def plot_predictions_with_history(days_history=30):
    """
    Son 30 günlük Bitcoin fiyatlarını ve üç modelin yarınki tahminlerini gösteren bir grafik çizer
    
    Args:
        days_history (int): Grafikte gösterilecek geçmiş gün sayısı
    """
    # Bugünün tarihi
    today = datetime.now().strftime("%Y-%m-%d")
    tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    
    # Geçmiş verileri indir
    start_date = (datetime.now() - timedelta(days=days_history+10)).strftime("%Y-%m-%d")
    btc_data = yf.download("BTC-USD", start=start_date, end=today)
    
    # Tüm modeller için tahminleri al
    predictions = {}
    for model_name in ["LinearRegression", "RandomForest", "LSTM"]:
        predictions[model_name] = predict_tomorrow_bitcoin_price(model_name)
    
    # Grafik çiz
    plt.figure(figsize=(12, 6))
    
    # Geçmiş fiyatları çiz
    plt.plot(btc_data.index[-days_history:], btc_data.Close[-days_history:], 'k-', label='Geçmiş Bitcoin Fiyatları')
    
    # Yarınki tahminleri çiz
    tomorrow_date = datetime.now() + timedelta(days=1)
    for model_name, prediction in predictions.items():
        plt.plot([btc_data.index[-1], tomorrow_date], [btc_data.Close[-1], prediction], 'o--', label=f'{model_name} Tahmini: ${prediction:.2f}')
    
    plt.title(f'Bitcoin Fiyat Tahmini - {tomorrow}')
    plt.xlabel('Tarih')
    plt.ylabel('Fiyat (USD)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # Grafik dosyasını kaydet
    plt.savefig('bitcoin_prediction.png')
    plt.show()
    
    print(f"\nTahmin sonuçları:")
    for model_name, prediction in predictions.items():
        print(f"{model_name}: ${prediction:.2f}")

# Ana program
if __name__ == "__main__":
    print("=== YARIN İÇİN BİTCOİN FİYAT TAHMİNLERİ ===\n")
    
    # Tek bir model için tahmin
    tahmin = predict_tomorrow_bitcoin_price("LSTM")
    print(f"\nLSTM modeline göre yarınki Bitcoin fiyatı: ${tahmin:.2f}")
    
    print("\n=== TÜM MODELLER İÇİN TAHMİNLER ===")
    # Tüm modeller için tahmin
    for model in ["LinearRegression", "RandomForest", "LSTM"]:
        predict_tomorrow_bitcoin_price(model)
    
    # Grafik çiz
    print("\n=== TAHMİN GRAFİĞİ OLUŞTURULUYOR ===")
    plot_predictions_with_history(30)

