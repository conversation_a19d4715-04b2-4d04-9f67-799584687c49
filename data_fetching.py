import yfinance as yf
import pandas as pd
import os
import csv

def download_bitcoin_data(start_date="2014-09-17", end_date="2023-05-21"):
    """
    Download Bitcoin price data from Yahoo Finance

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format

    Returns:
        DataFrame containing the Bitcoin data
    """
    print(f"Downloading Bitcoin data from {start_date} to {end_date}...")
    ticker_symbol = "BTC-USD"
    data = yf.download(ticker_symbol, start=start_date, end=end_date)

    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)

    # Save to CSV
    output_path = "data/btc_data.csv"
    data.to_csv(output_path)
    print(f"Bitcoin data saved to {output_path}")

    return data

def download_gold_data(start_date="2014-09-17", end_date="2025-06-01"):
    """
    Download Gold price data from Yahoo Finance

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format

    Returns:
        DataFrame containing the Gold data
    """
    print(f"Downloading Gold data from {start_date} to {end_date}...")
    ticker_symbol = "GC=F"
    data = yf.download(ticker_symbol, start=start_date, end=end_date)

    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)

    # Save to CSV
    output_path = "data/gold_data.csv"
    data.to_csv(output_path)
    print(f"Gold data saved to {output_path}")

    return data

def download_oil_data(start_date="2014-09-17", end_date="2025-06-01"):
    """
    Download Oil price data from Yahoo Finance

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format

    Returns:
        DataFrame containing the Oil data
    """
    print(f"Downloading Oil data from {start_date} to {end_date}...")
    ticker_symbol = "CL=F"
    data = yf.download(ticker_symbol, start=start_date, end=end_date)

    # Create data directory if it doesn't exist
    os.makedirs("data", exist_ok=True)

    # Save to CSV
    output_path = "data/oil_data.csv"
    data.to_csv(output_path)
    print(f"Oil data saved to {output_path}")

    return data

def download_all_data(start_date="2014-09-17", end_date="2025-06-01"):
    """
    Download Bitcoin, Gold, and Oil data

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
    """
    print("Downloading all datasets...")
    download_bitcoin_data(start_date, end_date)
    download_gold_data(start_date, end_date)
    download_oil_data(start_date, end_date)
    print("All datasets downloaded successfully!")

if __name__ == "__main__":
    # Example usage
    download_all_data()