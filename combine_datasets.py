import pandas as pd

def combine_datasets(btc_path, gold_path, oil_path, output_path):
    """
    Directly combine the datasets into a single dataset with unique column names,
    removing weekend data from Bitcoin to match gold and oil trading days.
    
    Parameters:
    btc_path (str): Path to the Bitcoin dataset
    gold_path (str): Path to the Gold dataset
    oil_path (str): Path to the Oil dataset
    output_path (str): Path to save the combined dataset
    """
    # Load the datasets
    print(f"Loading Bitcoin dataset from {btc_path}...")
    btc_df = pd.read_csv(btc_path)
    
    print(f"Loading Gold dataset from {gold_path}...")
    gold_df = pd.read_csv(gold_path)
    
    print(f"Loading Oil dataset from {oil_path}...")
    oil_df = pd.read_csv(oil_path)
    
    # Convert the 'Price' column to datetime in all datasets
    btc_df['Date'] = pd.to_datetime(btc_df['Price'])
    gold_df['Date'] = pd.to_datetime(gold_df['Price'])
    oil_df['Date'] = pd.to_datetime(oil_df['Price'])
    
    # Find common dates across all datasets
    btc_dates = set(btc_df['Date'])
    gold_dates = set(gold_df['Date'])
    oil_dates = set(oil_df['Date'])
    
    common_dates = btc_dates.intersection(gold_dates).intersection(oil_dates)
    print(f"Common dates across all datasets: {len(common_dates)}")
    
    # Filter datasets to include only common dates
    btc_df = btc_df[btc_df['Date'].isin(common_dates)].copy()
    gold_df = gold_df[gold_df['Date'].isin(common_dates)].copy()
    oil_df = oil_df[oil_df['Date'].isin(common_dates)].copy()
    
    # Rename columns in gold_df to add 'Gold_' prefix (except 'Price')
    gold_columns = {col: f'Gold_{col}' for col in gold_df.columns if col != 'Price'}
    gold_df = gold_df.rename(columns=gold_columns)
    
    # Rename columns in oil_df to add 'Oil_' prefix (except 'Price')
    oil_columns = {col: f'Oil_{col}' for col in oil_df.columns if col != 'Price'}
    oil_df = oil_df.rename(columns=oil_columns)
    
    # Merge the datasets on the 'Price' column
    print("Merging datasets...")
    merged_df = pd.merge(btc_df, gold_df, on='Price', how='inner')
    merged_df = pd.merge(merged_df, oil_df, on='Price', how='inner')
    
    # Remove all volume columns
    volume_cols = [col for col in merged_df.columns if 'Volume' in col]
    merged_df = merged_df.drop(columns=volume_cols)
    
    # Remove the temporary Date columns
    if 'Date' in merged_df.columns:
        merged_df = merged_df.drop('Date', axis=1)
    if 'Date_x' in merged_df.columns:
        merged_df = merged_df.drop('Date_x', axis=1)
    if 'Date_y' in merged_df.columns:
        merged_df = merged_df.drop('Date_y', axis=1)
    if 'Gold_Date' in merged_df.columns:
        merged_df = merged_df.drop('Gold_Date', axis=1)
    if 'Oil_Date' in merged_df.columns:
        merged_df = merged_df.drop('Oil_Date', axis=1)
    
    # Reorder columns to put 'Price' first
    cols = merged_df.columns.tolist()
    cols.remove('Price')
    cols = ['Price'] + cols
    merged_df = merged_df[cols]
    
    # Save the combined dataset
    print(f"Saving combined dataset to {output_path}...")
    merged_df.to_csv(output_path, index=False)
    
    # Print statistics
    print(f"Combined dataset has {len(merged_df)} rows and {len(merged_df.columns)} columns")
    print(f"Columns: {', '.join(merged_df.columns)}")

def main():
    # Combine the datasets directly
    combine_datasets(
        'data/btc_data.csv',
        'data/gold_data.csv',
        'data/oil_data.csv',
        'data/combined_dataset.csv'
    )
    
    print("\nDone! Datasets have been combined.")

if __name__ == "__main__":
    main()