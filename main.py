import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pandas_ta as ta
from sklearn.preprocessing import MinMaxScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import KFold
import joblib
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Input, LSTM, Dense, Activation

# Create models directory
if not os.path.exists("models"):
    os.makedirs("models")

# Load data
data = pd.read_csv("data/combined_dataset.csv", index_col="Price", parse_dates=True)

# Calculate technical indicators
data["RSI"] = ta.rsi(data.Close, length=15)
data["EMAF"] = ta.ema(data.Close, length=20)
data["EMAM"] = ta.ema(data.Close, length=100)
data["EMAS"] = ta.ema(data.Close, length=150)

# Calculate targets
data["TARGET"] = data["Close"] - data.Open
data["TARGET"] = data["TARGET"].shift(-1)
data["TargetClass"] = [1 if data.TARGET.iloc[i] > 0 else 0 for i in range(len(data))]
data["TargetNextClose"] = data["Close"].shift(-1)

# Clean data
data.dropna(inplace=True)
data.reset_index(inplace=True)
cols_to_drop = [col for col in ["Close", "Price"] if col in data.columns]
data.drop(cols_to_drop, axis=1, inplace=True)

# Save the processed data
data.to_csv("processed_data.csv", index=False)

# Prepare dataset
data_set = data.iloc[:, :-1]
y_data_set = data.iloc[:, -1]

# Scale data
sc_X = MinMaxScaler(feature_range=(0, 1))
data_set_scaled = sc_X.fit_transform(data_set)
sc_y = MinMaxScaler(feature_range=(0, 1))
y_data_set_scaled = sc_y.fit_transform(y_data_set.values.reshape(-1, 1))

# Create sequences for LSTM
backcandles = 30
X_seq = np.array(
    [
        data_set_scaled[i - backcandles : i]
        for i in range(backcandles, len(data_set_scaled))
    ]
)
y_seq = np.reshape(
    y_data_set_scaled[backcandles:], (len(y_data_set_scaled[backcandles:]), 1)
)

# Non-sequential data for LinearRegression and RandomForest
X = data_set_scaled[backcandles:]
y = y_data_set_scaled[backcandles:]


# Define models
def build_lstm_model(input_shape):
    lstm_input = Input(shape=input_shape, name="lstm_input")
    inputs = LSTM(150, name="first_layer")(lstm_input)
    inputs = Dense(1, name="dense_layer")(inputs)
    output = Activation("linear", name="output")(inputs)
    model = Model(inputs=lstm_input, outputs=output)
    model.compile(optimizer="adam", loss="mse")
    return model


models = {
    "LinearRegression": LinearRegression(),
    "RandomForest": RandomForestRegressor(n_estimators=300, random_state=42),
    "LSTM": build_lstm_model((backcandles, data_set_scaled.shape[1])),
}

# Initialize KFold
kf = KFold(n_splits=5, shuffle=True, random_state=42)

# K-Fold Cross-Validation for each model
for model_name, model in models.items():
    # Create model-specific directory
    model_dir = os.path.join("models", model_name)
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)

    mse_scores = []
    mae_scores = []
    r2_scores = []

    for fold, (train_index, test_index) in enumerate(
        kf.split(X_seq if model_name == "LSTM" else X)
    ):
        # Select appropriate data for the model
        if model_name == "LSTM":
            X_train, X_test = X_seq[train_index], X_seq[test_index]
            y_train, y_test = y_seq[train_index], y_seq[test_index]
        else:
            X_train, X_test = X[train_index], X[test_index]
            y_train, y_test = y[train_index], y[test_index]

        # Train model
        if model_name == "LSTM":
            model.fit(X_train, y_train, epochs=50, batch_size=32, verbose=0)
        else:
            model.fit(X_train, y_train.ravel())

        # Predict and calculate metrics
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        mse_scores.append(mse)
        mae_scores.append(mae)
        r2_scores.append(r2)

        # Save model and scalers for the last fold
        if fold == 4:  # Save only for the last fold
            model_path = os.path.join(
                model_dir,
                (
                    f"{model_name}_model.pkl"
                    if model_name != "LSTM"
                    else f"{model_name}_model.h5"
                ),
            )
            if model_name == "LSTM":
                model.save(model_path)
            else:
                joblib.dump(model, model_path)
            joblib.dump(sc_X, os.path.join(model_dir, f"{model_name}_scaler_X.pkl"))
            joblib.dump(sc_y, os.path.join(model_dir, f"{model_name}_scaler_y.pkl"))

            # Plot test results
            y_test_unscaled = sc_y.inverse_transform(y_test)
            y_pred_unscaled = sc_y.inverse_transform(y_pred.reshape(-1, 1))

            plt.figure(figsize=(16, 8))
            plt.plot(y_test_unscaled, color="black", label="Test")
            plt.plot(y_pred_unscaled, color="green", label="Prediction")
            plt.title(f"{model_name} Test Predictions")
            plt.legend()
            plt.savefig(os.path.join(model_dir, f"{model_name}_test_predictions.png"))
            plt.close()

            # Predict next 30 days
            future_predictions = []
            if model_name == "LSTM":
                last_available_data = X_test[-1].reshape(1, backcandles, -1)
            else:
                last_available_data = X_test[-1].reshape(1, -1)

            for _ in range(30):
                next_day_prediction = (
                    model.predict(last_available_data, verbose=0)[0]
                    if model_name == "LSTM"
                    else model.predict(last_available_data)[0]
                )
                future_predictions.append(next_day_prediction)
                if model_name == "LSTM":
                    last_available_data = np.roll(last_available_data, -1, axis=1)
                    last_available_data[0, -1, -1] = next_day_prediction
                else:
                    last_available_data = np.roll(last_available_data, -1)
                    last_available_data[0, -1] = next_day_prediction

            # Inverse transform future predictions
            future_predictions_unscaled = sc_y.inverse_transform(
                np.array(future_predictions).reshape(-1, 1)
            )

            # Plot future predictions
            plt.figure(figsize=(16, 8))
            plt.plot(
                np.arange(len(y_test_unscaled)),
                y_test_unscaled,
                color="black",
                label="Test Data",
            )
            plt.plot(
                np.arange(len(y_test_unscaled), len(y_test_unscaled) + 30),
                future_predictions_unscaled,
                color="green",
                label="Future Predictions",
            )
            plt.title(f"{model_name} Future Predictions")
            plt.legend()
            plt.savefig(os.path.join(model_dir, f"{model_name}_future_predictions.png"))
            plt.close()

    # Save model-specific performance metrics
    results = {
        "Fold": list(range(1, 6)),
        "MSE": mse_scores,
        "MAE": mae_scores,
        "R2": r2_scores,
    }
    results_df = pd.DataFrame(results)
    results_df["Mean_MSE"] = np.mean(mse_scores)
    results_df["Mean_MAE"] = np.mean(mae_scores)
    results_df["Mean_R2"] = np.mean(r2_scores)
    results_df.to_csv(
        os.path.join(model_dir, f"{model_name}_performance.csv"), index=False
    )

    # Print results
    print(f"\nModel: {model_name}")
    print(f"MSE scores: {mse_scores}")
    print(f"Mean MSE: {np.mean(mse_scores):.4f}")
    print(f"MAE scores: {mae_scores}")
    print(f"Mean MAE: {np.mean(mae_scores):.4f}")
    print(f"R2 scores: {r2_scores}")
    print(f"Mean R2: {np.mean(r2_scores):.4f}")